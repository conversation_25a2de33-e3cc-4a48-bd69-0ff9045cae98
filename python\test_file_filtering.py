#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試檔案過濾功能
"""

from 資料清理 import 尋找檔案
from pathlib import Path
import os

def test_file_filtering():
    """測試檔案過濾功能"""
    print("🧪 測試檔案過濾功能")
    print("="*50)
    
    # 測試目錄
    test_dir = Path("../學生")  # 假設學生資料夾存在
    
    if not test_dir.exists():
        print(f"⚠️  測試目錄不存在: {test_dir}")
        return
    
    print(f"📁 測試目錄: {test_dir}")
    
    # 列出目錄中的所有檔案
    print(f"\n📋 目錄中的所有檔案:")
    try:
        all_items = os.listdir(test_dir)
        for item in all_items:
            item_path = test_dir / item
            if item_path.is_file():
                file_type = "📄"
                if item.startswith("~$"):
                    file_type = "🚫 Excel臨時檔案"
                elif item.startswith("."):
                    file_type = "👁️ 隱藏檔案"
                elif "已整合-" in item:
                    file_type = "📊 已整合檔案"
                elif item.endswith(('.xlsx', '.xls')):
                    file_type = "📗 Excel檔案"
                elif item.endswith('.csv'):
                    file_type = "📄 CSV檔案"
                
                print(f"  {file_type} {item}")
    except Exception as e:
        print(f"❌ 列出檔案時發生錯誤: {e}")
        return
    
    # 測試過濾後的檔案
    print(f"\n🔍 使用 尋找檔案 函數過濾後的結果:")
    try:
        # 尋找 Excel 檔案
        excel_files = 尋找檔案(test_dir, extensions=['.xlsx', '.xls'])
        print(f"📗 找到 {len(excel_files)} 個 Excel 檔案:")
        for file_path in excel_files:
            print(f"  ✅ {file_path.name}")
        
        # 尋找特定關鍵字的檔案
        student_files = 尋找檔案(test_dir, file_keywords=['學生'], extensions=['.xlsx', '.xls'])
        print(f"\n👥 找到 {len(student_files)} 個包含'學生'關鍵字的檔案:")
        for file_path in student_files:
            print(f"  ✅ {file_path.name}")
            
    except Exception as e:
        print(f"❌ 過濾檔案時發生錯誤: {e}")

def test_filtering_logic():
    """測試過濾邏輯"""
    print(f"\n🧪 測試過濾邏輯:")
    print("="*50)
    
    # 測試檔案名稱
    test_files = [
        "在學學生數.xlsx",           # ✅ 正常檔案
        "~$在學學生數.xlsx",         # ❌ Excel臨時檔案
        ".DS_Store",                # ❌ 隱藏檔案
        "已整合-學生歷年版.xlsx",     # ❌ 已整合檔案
        "外國學生數.xlsx",           # ✅ 正常檔案
        "~$外國學生數.xlsx",         # ❌ Excel臨時檔案
        "test.txt",                 # ❌ 非Excel檔案
        "資料.csv"                  # ❌ 非Excel檔案（如果只要Excel）
    ]
    
    print("檔案過濾測試:")
    for filename in test_files:
        should_include = True
        reason = "✅ 正常檔案"
        
        if "已整合-" in filename:
            should_include = False
            reason = "❌ 已整合檔案"
        elif filename.startswith("~$"):
            should_include = False
            reason = "❌ Excel臨時檔案"
        elif filename.startswith("."):
            should_include = False
            reason = "❌ 隱藏檔案"
        elif not filename.endswith(('.xlsx', '.xls')):
            should_include = False
            reason = "❌ 非Excel檔案"
        
        print(f"  {filename:<30} {reason}")

if __name__ == "__main__":
    print("🚀 檔案過濾功能測試")
    print("="*60)
    
    # 測試過濾邏輯
    test_filtering_logic()
    
    # 測試實際檔案過濾
    test_file_filtering()
    
    print(f"\n🎉 測試完成！")
    print(f"💡 現在程式會自動跳過:")
    print(f"  • Excel臨時檔案 (以 ~$ 開頭)")
    print(f"  • 隱藏檔案 (以 . 開頭)")
    print(f"  • 已整合檔案 (包含 '已整合-')")
